defmodule Drops.MultiAdapterTest do
  use Drops.RelationCase, async: true

  describe "single adapter tests" do
    @tag relations: [:users], adapter: :sqlite
    test "works with SQLite adapter", %{users: users} do
      assert users != nil
      assert users.ecto_schema(:fields) == [:id, :name, :email, :inserted_at, :updated_at]
    end

    @tag relations: [:users], adapter: :postgres
    test "works with PostgreSQL adapter", %{users: users} do
      assert users != nil
      assert users.ecto_schema(:fields) == [:id, :name, :email, :inserted_at, :updated_at]
    end
  end

  adapters([:sqlite, :postgres]) do
    @tag relations: [:users]
    test "basic schema inference works", %{users: users} do
      assert users != nil
      assert users.ecto_schema(:fields) == [:id, :name, :email, :inserted_at, :updated_at]
      assert users.ecto_schema(:type, :id) == :id
      assert users.ecto_schema(:type, :name) == :string
      assert users.ecto_schema(:type, :email) == :string
    end

    @tag relations: [:users]
    test "can query data", %{users: users} do
      # Test that we can call basic query functions
      assert users.all() == []
      assert users.count() == 0
    end
  end

  describe "default adapter behavior" do
    @tag relations: [:users]
    test "defaults to SQLite when no adapter specified", %{users: users} do
      # This should use SQLite by default
      assert users != nil
      assert users.ecto_schema(:fields) == [:id, :name, :email, :inserted_at, :updated_at]
    end
  end
end
