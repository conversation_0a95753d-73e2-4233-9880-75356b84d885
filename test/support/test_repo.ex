defmodule Drops.Repos.Sqlite do
  @moduledoc """
  SQLite repository for Ecto operations in test and dev environments.

  This repo is configured to use SQLite database for testing and development.
  """

  use Ecto.Repo,
    otp_app: :drops,
    adapter: Ecto.Adapters.SQLite3
end

defmodule Drops.Repos.Postgres do
  @moduledoc """
  PostgreSQL repository for Ecto operations in test and dev environments.

  This repo is configured to use PostgreSQL database for testing and development.
  """

  use Ecto.Repo,
    otp_app: :drops,
    adapter: Ecto.Adapters.Postgres
end

# Keep the original TestRepo as a simple SQLite repo for backward compatibility
defmodule Drops.TestRepo do
  use Ecto.Repo,
    otp_app: :drops,
    adapter: Ecto.Adapters.SQLite3
end
