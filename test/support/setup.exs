Code.require_file("test_repo.ex", __DIR__)
Code.require_file("test_config.ex", __DIR__)

Code.require_file("doctest_case.ex", __DIR__)
Code.require_file("data_case.ex", __DIR__)
Code.require_file("contract_case.ex", __DIR__)
Code.require_file("operation_case.ex", __DIR__)
Code.require_file("relation_case.ex", __DIR__)

Code.require_file("ecto/test_schemas.ex", __DIR__)
Code.require_file("ecto/user_group_schemas.ex", __DIR__)

Application.ensure_all_started(:drops)

# Start the repos for dev/test environments
if Mix.env() in [:dev, :test] do
  # Load ExampleSetup for examples (only in dev)
  if Mix.env() == :dev do
    Code.require_file("../../examples/setup.exs", __DIR__)
  end

  {:ok, _} = Application.ensure_all_started(:ecto_sql)

  # Start the TestRepo (SQLite) for backward compatibility
  case Drops.TestRepo.start_link() do
    {:ok, _pid} -> :ok
    {:error, {:already_started, _pid}} -> :ok
    error -> raise "Failed to start TestRepo: #{inspect(error)}"
  end

  # Start the SQLite repo if it's not already started
  case Drops.Repos.Sqlite.start_link() do
    {:ok, _pid} -> :ok
    {:error, {:already_started, _pid}} -> :ok
    error -> raise "Failed to start Sqlite repo: #{inspect(error)}"
  end

  # Start the PostgreSQL repo if it's not already started
  case Drops.Repos.Postgres.start_link() do
    {:ok, _pid} -> :ok
    {:error, {:already_started, _pid}} -> :ok
    error -> raise "Failed to start Postgres repo: #{inspect(error)}"
  end
end
