defmodule Drops.RelationCase do
  @moduledoc """
  Test case template for Drops.Relation tests.

  This module provides a convenient way to test relation modules with automatic
  schema cache management and relation setup.

  ## Usage

      defmodule MyRelationTest do
        use Drops.RelationCase, async: true

        describe "my relation tests" do
          @tag relations: [:users], adapter: :sqlite
          test "basic test", %{users: users} do
            # users relation is automatically available
          end

          # Or use the relation macro directly
          relation(:posts)

          test "posts test", %{posts: posts} do
            # posts relation is available
          end
        end
      end

  ## Multi-Adapter Testing

      defmodule MyMultiAdapterTest do
        use Drops.RelationCase, async: true

        adapters([:sqlite, :postgres]) do
          @tag relations: [:users]
          test "works with both adapters", %{users: users} do
            # This test will run for both SQLite and PostgreSQL
          end
        end
      end

  ## Features

  - Automatic Ecto sandbox setup
  - Schema cache clearing for test isolation
  - Relation macro for defining test relations
  - Support for @tag relations: [...] and @describetag relations: [...]
  - Multi-adapter testing with adapters/2 macro
  - Automatic migration running for test tables
  - Adapter selection via @tag adapter: :sqlite/:postgres (defaults to :sqlite)
  """

  use ExUnit.CaseTemplate

  using do
    quote do
      use Drops.ContractCase

      alias Drops.TestRepo
      alias Drops.Repos.Sqlite
      alias Drops.Repos.Postgres

      import Ecto
      import Ecto.Changeset
      import Ecto.Query
      import Drops.RelationCase

      # Clear schema cache before each test
      setup do
        if Process.whereis(Drops.Relation.SchemaCache) do
          Drops.Relation.SchemaCache.clear_all()
        end

        :ok
      end
    end
  end

  setup tags do
    # Determine adapter from tags (defaults to :sqlite)
    adapter = tags[:adapter] || :sqlite

    # Set up Ecto sandbox
    setup_sandbox(tags, adapter)

    # Run migrations for test environment
    run_migrations(adapter)

    # Handle relation tags
    context = handle_relation_tags(tags, adapter)

    {:ok, context}
  end

  @doc """
  Defines a relation for testing.

  ## Examples

      relation(:users)
      relation(:posts) do
        # Custom relation configuration
      end
  """
  defmacro relation(name, _opts \\ []) do
    relation_name = name |> Atom.to_string() |> Macro.camelize()
    _table_name = Atom.to_string(name)

    quote do
      setup context do
        adapter = context[:adapter] || :sqlite

        # Define the relation module dynamically based on adapter
        relation_module_name =
          Module.concat([
            Test,
            Relations,
            "#{unquote(relation_name)}#{String.capitalize(Atom.to_string(adapter))}"
          ])

        repo_module =
          case adapter do
            :sqlite -> Drops.Repos.Sqlite
            :postgres -> Drops.Repos.Postgres
          end

        # Create the relation module dynamically
        unless Code.ensure_loaded?(relation_module_name) do
          {:module, ^relation_module_name, _bytecode, _result} =
            Module.create(
              relation_module_name,
              quote do
                use Drops.Relation,
                  repo: unquote(repo_module),
                  name: unquote(table_name),
                  infer: true
              end,
              Macro.Env.location(__ENV__)
            )
        end

        # Add relation to context
        relation_context = Map.put(context, unquote(name), relation_module_name)

        on_exit(fn ->
          # Clean up the module
          try do
            :code.purge(relation_module_name)
            :code.delete(relation_module_name)
          rescue
            _ -> :ok
          end
        end)

        {:ok, relation_context}
      end
    end
  end

  @doc """
  Runs tests for multiple adapters.

  ## Examples

      adapters([:sqlite, :postgres]) do
        @tag relations: [:users]
        test "works with both adapters", %{users: users} do
          # This test will run for both SQLite and PostgreSQL
        end
      end
  """
  defmacro adapters(adapter_list, do: block) do
    for adapter <- adapter_list do
      quote do
        describe "with #{unquote(adapter)} adapter" do
          setup do
            {:ok, adapter: unquote(adapter)}
          end

          unquote(block)
        end
      end
    end
  end

  @doc """
  Sets up the sandbox based on the test tags and adapter.
  """
  def setup_sandbox(tags, adapter) do
    repo = get_repo_for_adapter(adapter)
    pid = Ecto.Adapters.SQL.Sandbox.start_owner!(repo, shared: not tags[:async])
    on_exit(fn -> Ecto.Adapters.SQL.Sandbox.stop_owner(pid) end)
  end

  @doc """
  Runs all test migrations for the specified adapter.
  """
  def run_migrations(adapter) do
    repo = get_repo_for_adapter(adapter)

    # Check if tables exist based on adapter
    table_check_query =
      case adapter do
        :sqlite ->
          "SELECT name FROM sqlite_master WHERE type='table' AND name='users';"

        :postgres ->
          "SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename = 'users';"
      end

    # Run migrations if they haven't been run yet
    case Ecto.Adapters.SQL.query(repo, table_check_query) do
      {:ok, %{rows: []}} ->
        # Tables don't exist, run migrations
        # Suppress warnings about redefining migration modules
        Code.compiler_options(ignore_module_conflict: true)

        try do
          # Create schema_migrations table manually if it doesn't exist
          case adapter do
            :sqlite ->
              Ecto.Adapters.SQL.query(repo, """
                CREATE TABLE IF NOT EXISTS schema_migrations (
                  version BIGINT PRIMARY KEY,
                  inserted_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
              """)

            :postgres ->
              Ecto.Adapters.SQL.query(repo, """
                CREATE TABLE IF NOT EXISTS schema_migrations (
                  version BIGINT PRIMARY KEY,
                  inserted_at TIMESTAMP DEFAULT NOW()
                );
              """)
          end

          Ecto.Migrator.run(repo, migrations_path(adapter), :up,
            all: true,
            log: :info,
            log_migrations_sql: false,
            log_migrator_sql: false
          )
        after
          Code.compiler_options(ignore_module_conflict: false)
        end

      _ ->
        # Tables already exist, skip
        :ok
    end
  end

  @doc """
  Handles @tag relations: [...] and @describetag relations: [...] syntax.
  """
  def handle_relation_tags(tags, adapter) do
    relations = tags[:relations] || []
    repo = get_repo_for_adapter(adapter)

    Enum.reduce(relations, %{}, fn relation_name, context ->
      relation_name_string = Atom.to_string(relation_name)
      relation_module_name = relation_name_string |> Macro.camelize()

      module_name =
        Module.concat([
          Test,
          Relations,
          "#{relation_module_name}#{String.capitalize(Atom.to_string(adapter))}"
        ])

      # Define the relation module dynamically
      # We need to use Module.create/3 for runtime module creation
      # Check if module already exists to avoid redefinition warnings
      unless Code.ensure_loaded?(module_name) do
        {:module, ^module_name, _bytecode, _result} =
          Module.create(
            module_name,
            quote do
              use Drops.Relation,
                repo: unquote(repo),
                name: unquote(relation_name_string),
                infer: true
            end,
            Macro.Env.location(__ENV__)
          )
      end

      # Add to context
      Map.put(context, relation_name, module_name)
    end)
  end

  @doc """
  Gets the appropriate repo module for the given adapter.
  """
  def get_repo_for_adapter(:sqlite), do: Drops.TestRepo
  def get_repo_for_adapter(:postgres), do: Drops.Repos.Postgres

  defp migrations_path(adapter) do
    Application.app_dir(:drops, "priv/repo/test/#{adapter}/migrations")
  end
end
