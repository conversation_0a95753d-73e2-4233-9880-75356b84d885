defmodule Drops.RelationCase do
  @moduledoc """
  Test case template for Drops.Relation tests.

  This module provides a convenient way to test relation modules with automatic
  schema cache management and relation setup.

  ## Usage

      defmodule MyRelationTest do
        use Drops.RelationCase, async: true

        describe "my relation tests" do
          @tag relations: [:users], adapter: :sqlite
          test "basic test", %{users: users} do
            # users relation is automatically available
          end

          # Or use the relation macro directly
          relation(:posts)

          test "posts test", %{posts: posts} do
            # posts relation is available
          end
        end
      end

  ## Multi-Adapter Testing

      defmodule MyMultiAdapterTest do
        use Drops.RelationCase, async: true

        adapters([:sqlite, :postgres]) do
          @tag relations: [:users]
          test "works with both adapters", %{users: users} do
            # This test will run for both SQLite and PostgreSQL
          end
        end
      end

  ## Features

  - Automatic Ecto sandbox setup
  - Schema cache clearing for test isolation
  - Relation macro for defining test relations
  - Support for @tag relations: [...] and @describetag relations: [...]
  - Multi-adapter testing with adapters/2 macro
  - Automatic migration running for test tables
  - Adapter selection via @tag adapter: :sqlite/:postgres (defaults to :sqlite)
  """

  use ExUnit.CaseTemplate

  using do
    quote do
      use Drops.ContractCase

      alias Drops.TestRepo
      alias Drops.Repos.Sqlite
      alias Drops.Repos.Postgres

      import Ecto
      import Ecto.Changeset
      import Ecto.Query
      import Drops.RelationCase

      # Clear schema cache before each test
      setup do
        if Process.whereis(Drops.Relation.SchemaCache) do
          Drops.Relation.SchemaCache.clear_all()
        end

        :ok
      end
    end
  end

  setup tags do
    # Determine adapter from tags (defaults to :sqlite)
    adapter = tags[:adapter] || :sqlite

    # Ensure tmp directory exists for SQLite databases
    if adapter == :sqlite do
      File.mkdir_p!("tmp")
    end

    # Set up Ecto sandbox
    setup_sandbox(tags, adapter)

    # Run migrations for test environment
    run_migrations(adapter)

    # Handle relation tags
    context = handle_relation_tags(tags, adapter)

    {:ok, context}
  end

  @doc """
  Defines a relation for testing.

  ## Examples

      relation(:users)
      relation(:posts) do
        # Custom relation configuration
      end
  """
  defmacro relation(name, _opts \\ []) do
    relation_name = name |> Atom.to_string() |> Macro.camelize()
    _table_name = Atom.to_string(name)

    quote do
      setup context do
        adapter = context[:adapter] || :sqlite

        # Define the relation module dynamically based on adapter
        relation_module_name =
          Module.concat([
            Test,
            Relations,
            "#{unquote(relation_name)}#{String.capitalize(Atom.to_string(adapter))}"
          ])

        repo_module =
          case adapter do
            :sqlite -> Drops.Repos.Sqlite
            :postgres -> Drops.Repos.Postgres
          end

        # Create the relation module dynamically
        unless Code.ensure_loaded?(relation_module_name) do
          {:module, ^relation_module_name, _bytecode, _result} =
            Module.create(
              relation_module_name,
              quote do
                use Drops.Relation,
                  repo: unquote(repo_module),
                  name: unquote(table_name),
                  infer: true
              end,
              Macro.Env.location(__ENV__)
            )
        end

        # Add relation to context
        relation_context = Map.put(context, unquote(name), relation_module_name)

        on_exit(fn ->
          # Clean up the module
          try do
            :code.purge(relation_module_name)
            :code.delete(relation_module_name)
          rescue
            _ -> :ok
          end
        end)

        {:ok, relation_context}
      end
    end
  end

  @doc """
  Runs tests for multiple adapters.

  ## Examples

      adapters([:sqlite, :postgres]) do
        @tag relations: [:users]
        test "works with both adapters", %{users: users} do
          # This test will run for both SQLite and PostgreSQL
        end
      end
  """
  defmacro adapters(adapter_list, do: block) do
    for adapter <- adapter_list do
      quote do
        describe "with #{unquote(adapter)} adapter" do
          setup do
            {:ok, adapter: unquote(adapter)}
          end

          unquote(block)
        end
      end
    end
  end

  @doc """
  Sets up the sandbox based on the test tags and adapter.
  """
  def setup_sandbox(tags, adapter) do
    repo = get_repo_for_adapter(adapter)
    pid = Ecto.Adapters.SQL.Sandbox.start_owner!(repo, shared: not tags[:async])
    on_exit(fn -> Ecto.Adapters.SQL.Sandbox.stop_owner(pid) end)
  end

  @doc """
  Creates test tables for the specified adapter using direct SQL.
  This avoids migration module conflicts while still setting up the test schema.
  """
  def run_migrations(adapter) do
    repo = get_repo_for_adapter(adapter)

    # Use a process-based cache to track which adapters have had tables created
    cache_key = {:tables_created, adapter}

    case Process.get(cache_key) do
      true ->
        # Tables already created for this adapter in this process
        :ok

      _ ->
        # Create tables for this adapter
        create_test_tables(repo, adapter)
        # Mark tables as created for this adapter
        Process.put(cache_key, true)
        :ok
    end
  end

  defp create_test_tables(repo, adapter) do
    # Create tables directly using SQL to avoid migration module conflicts
    table_definitions = get_table_definitions(adapter)

    Enum.each(table_definitions, fn sql ->
      case Ecto.Adapters.SQL.query(repo, sql) do
        {:ok, _} ->
          :ok

        {:error, error} ->
          # If table already exists, that's fine
          error_msg = to_string(error)

          if String.contains?(error_msg, "already exists") or
               String.contains?(error_msg, "duplicate column name") do
            :ok
          else
            raise "Failed to create table: #{inspect(error)}"
          end
      end
    end)
  end

  defp get_table_definitions(:sqlite) do
    [
      """
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        email TEXT,
        inserted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS groups (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        inserted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS user_groups (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        group_id INTEGER,
        inserted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (group_id) REFERENCES groups(id)
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS posts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT,
        body TEXT,
        user_id INTEGER,
        inserted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS comments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        body TEXT,
        post_id INTEGER,
        user_id INTEGER,
        inserted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (post_id) REFERENCES posts(id),
        FOREIGN KEY (user_id) REFERENCES users(id)
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS basic_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        string_field TEXT,
        integer_field INTEGER,
        float_field REAL,
        boolean_field INTEGER,
        date_field TEXT,
        datetime_field TEXT,
        time_field TEXT,
        binary_field BLOB,
        bitstring_field BLOB,
        inserted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS uuid_pk_table (
        id TEXT PRIMARY KEY,
        name TEXT,
        inserted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS composite_pk_table (
        key1 TEXT,
        key2 TEXT,
        name TEXT,
        inserted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (key1, key2)
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS timestamps_table (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        custom_inserted_at TEXT,
        custom_updated_at TEXT
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS custom_pk (
        uuid_id TEXT PRIMARY KEY,
        name TEXT,
        inserted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS no_pk (
        name TEXT,
        value TEXT
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS associations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        parent_id INTEGER,
        inserted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES association_parents(id)
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS association_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        association_id INTEGER,
        inserted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (association_id) REFERENCES associations(id)
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS association_parents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        inserted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS timestamps (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        custom_inserted_at TEXT,
        custom_updated_at TEXT
      );
      """
    ]
  end

  defp get_table_definitions(:postgres) do
    [
      """
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255),
        email VARCHAR(255),
        inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS groups (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255),
        inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS user_groups (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        group_id INTEGER REFERENCES groups(id),
        inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS posts (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255),
        body TEXT,
        user_id INTEGER REFERENCES users(id),
        inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS comments (
        id SERIAL PRIMARY KEY,
        body TEXT,
        post_id INTEGER REFERENCES posts(id),
        user_id INTEGER REFERENCES users(id),
        inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS basic_types (
        id SERIAL PRIMARY KEY,
        string_field VARCHAR(255),
        integer_field INTEGER,
        float_field REAL,
        boolean_field BOOLEAN,
        date_field DATE,
        datetime_field TIMESTAMP,
        time_field TIME,
        binary_field BYTEA,
        bitstring_field BYTEA,
        inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS uuid_pk_table (
        id UUID PRIMARY KEY,
        name VARCHAR(255),
        inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS composite_pk_table (
        key1 VARCHAR(255),
        key2 VARCHAR(255),
        name VARCHAR(255),
        inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        PRIMARY KEY (key1, key2)
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS timestamps_table (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255),
        custom_inserted_at TIMESTAMP,
        custom_updated_at TIMESTAMP
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS custom_pk (
        uuid_id UUID PRIMARY KEY,
        name VARCHAR(255),
        inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS no_pk (
        name VARCHAR(255),
        value VARCHAR(255)
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS associations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255),
        parent_id INTEGER REFERENCES association_parents(id),
        inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS association_items (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255),
        association_id INTEGER REFERENCES associations(id),
        inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS association_parents (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255),
        inserted_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
      """,
      """
      CREATE TABLE IF NOT EXISTS timestamps (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255),
        custom_inserted_at TIMESTAMP,
        custom_updated_at TIMESTAMP
      );
      """
    ]
  end

  @doc """
  Handles @tag relations: [...] and @describetag relations: [...] syntax.
  """
  def handle_relation_tags(tags, adapter) do
    relations = tags[:relations] || []
    repo = get_repo_for_adapter(adapter)

    Enum.reduce(relations, %{}, fn relation_name, context ->
      relation_name_string = Atom.to_string(relation_name)
      relation_module_name = relation_name_string |> Macro.camelize()

      module_name =
        Module.concat([
          Test,
          Relations,
          "#{relation_module_name}#{String.capitalize(Atom.to_string(adapter))}"
        ])

      # Define the relation module dynamically
      # We need to use Module.create/3 for runtime module creation
      # Check if module already exists to avoid redefinition warnings
      unless Code.ensure_loaded?(module_name) do
        {:module, ^module_name, _bytecode, _result} =
          Module.create(
            module_name,
            quote do
              use Drops.Relation,
                repo: unquote(repo),
                name: unquote(relation_name_string),
                infer: true
            end,
            Macro.Env.location(__ENV__)
          )
      end

      # Add to context
      Map.put(context, relation_name, module_name)
    end)
  end

  @doc """
  Gets the appropriate repo module for the given adapter.
  """
  def get_repo_for_adapter(:sqlite), do: Drops.Repos.Sqlite
  def get_repo_for_adapter(:postgres), do: Drops.Repos.Postgres
end
