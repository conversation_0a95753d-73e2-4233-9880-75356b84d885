defmodule Mix.Tasks.Drops.Ecto do
  @moduledoc """
  Wrapper for Ecto tasks with adapter selection support.

  This task allows running Ecto commands with a specific adapter.
  Defaults to SQLite if no adapter is specified.

  ## Usage

      mix drops.ecto create --adapter sqlite
      mix drops.ecto migrate --adapter postgres
      mix drops.ecto setup --adapter sqlite

  ## Available adapters

  - sqlite (default)
  - postgres

  ## Available commands

  - create
  - drop
  - migrate
  - rollback
  - setup
  - reset
  - gen.migration

  """

  use Mix.Task

  @shortdoc "Run Ecto commands with adapter selection"

  @impl Mix.Task
  def run(args) do
    {opts, remaining_args} = parse_args(args)
    adapter = opts[:adapter] || :sqlite

    # If no command provided, default to setup
    command =
      case remaining_args do
        [] -> "setup"
        [cmd | _] -> cmd
      end

    # Set the repo based on adapter
    repo =
      case adapter do
        :sqlite -> "Drops.Repos.Sqlite"
        :postgres -> "Drops.Repos.Postgres"
        _ -> Mix.raise("Unknown adapter: #{adapter}. Available: sqlite, postgres")
      end

    # Set environment variable for <PERSON><PERSON><PERSON> to use the correct repo
    System.put_env("ECTO_REPO", repo)

    # Run the setup task first
    Mix.Task.run("drops.dev.setup")

    # Run the appropriate Ecto command
    # Get additional args after the command
    additional_args =
      case remaining_args do
        [_command | rest] -> rest
        [] -> []
      end

    case command do
      "create" ->
        Mix.Task.run("ecto.create", ["--repo", repo] ++ additional_args)

      "drop" ->
        Mix.Task.run("ecto.drop", ["--repo", repo] ++ additional_args)

      "migrate" ->
        Mix.Task.run("ecto.migrate", ["--repo", repo] ++ additional_args)

      "rollback" ->
        Mix.Task.run("ecto.rollback", ["--repo", repo] ++ additional_args)

      "setup" ->
        Mix.Task.run("ecto.create", ["--repo", repo])
        Mix.Task.run("ecto.migrate", ["--repo", repo])

      "reset" ->
        Mix.Task.run("ecto.drop", ["--repo", repo])
        Mix.Task.run("ecto.create", ["--repo", repo])
        Mix.Task.run("ecto.migrate", ["--repo", repo])

      "gen.migration" ->
        if length(additional_args) < 1 do
          Mix.raise("gen.migration requires a migration name")
        end

        Mix.Task.run(
          "ecto.gen.migration",
          ["--repo", repo] ++ additional_args
        )

      _ ->
        Mix.raise("Unknown command: #{command}")
    end
  end

  defp parse_args(args) do
    {opts, remaining, _} =
      OptionParser.parse(args,
        switches: [adapter: :string],
        aliases: [a: :adapter]
      )

    # Convert adapter string to atom
    opts =
      if opts[:adapter] do
        Keyword.put(opts, :adapter, String.to_atom(opts[:adapter]))
      else
        opts
      end

    {opts, remaining}
  end
end
